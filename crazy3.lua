local function init()
    if not IsAlive() then 
        return false 
    end
    
    local skillDropdowns = {
        Options.supportStyle.Value,
        Options.fightingStyle.Value,
        Options.swordStyle.Value,
        Options.gunStyle.Value,
        Options.devilFruit.Value
    }
    
    local hasSkills = false
    for _, skillValue in pairs(skillDropdowns) do
        if (type(skillValue) == "table" and next(skillValue) ~= nil) or 
           (type(skillValue) == "string" and skillValue ~= nil) then
            hasSkills = true
            break
        end
    end
    
    if not hasSkills and lastNotification ~= "empty" then
        lastNotification = "empty"
        return Library:Notify('Please select some skills to use first!')
    elseif hasSkills and lastNotification ~= "hasSkills" then
        lastNotification = "hasSkills"
        Library:Notify(placeType == 'Sea' and 'Starting Auto Farm...' or 'Starting Gamemode...')
    end
    
    if not hasSkills then return false end
    
    if placeType == 'Sea' and Options.selectedMob.Value == nil then
        return Library:Notify('Please select a mob first!')
    end
    
    if not IsAlive() then return false end
    
    local target = findOptimalEnemy()
    if not target or not target:FindFirstChild('HumanoidRootPart') or not target:FindFirstChild('Head') then
        if placeType == 'Sea' then
            repeat
                task.wait()
            until target
        elseif placeType == 'Gamemode' and identifyGamemode() == 'Golden Heist' then
            local jackpot = Workspace:FindFirstChild('Jackpot')
            if jackpot and not jackpot.Value then
                return false
            else
                return Library:Notify('No enemies found nearby!')
            end
        else
            return Library:Notify('No enemies found nearby!')
        end
    end
    
    if not IsAlive() then return false end
    
    if Options.mobilityMethod.Value == 'Teleport' then
        Teleport(target.HumanoidRootPart)
    elseif Options.mobilityMethod.Value == 'Tween' then
        Tween(target.HumanoidRootPart)
        task.wait(0.5)
    end
    
    if not IsAlive() then return false end
    
    local skillCategories = {
        {Options.supportStyle.Value, "Support Style"},
        {Options.fightingStyle.Value, "Fighting Style"},
        {Options.swordStyle.Value, "Sword Style"},
        {Options.gunStyle.Value, "Gun Style"},
        {Options.devilFruit.Value, "Devil Fruit"}
        {option}
    }
    
    for _, category in pairs(skillCategories) do
        if not IsAlive() then return false end
        
        local skillValue, styleName = category[1], category[2]
        
        if type(skillValue) == "table" then
            for skillKey, _ in pairs(skillValue) do
                if not IsAlive() then return false end
                Remotes.requestAbility:FireServer(styleName, skillKey, target.HumanoidRootPart.CFrame, target.Head, 5)
                task.wait(0.1)
            end
        elseif type(skillValue) == "string" and skillValue ~= nil then
            if not IsAlive() then return false end
            Remotes.requestAbility:FireServer(styleName, skillValue, target.HumanoidRootPart.CFrame, target.Head, 5)
            task.wait(0.1)
        end
    end
    
    return true
end

local function doesMobHaveAQuest(mob)
    if not Dictionaries.Mobs[placeName] then
        warn("Error: placeName '" .. tostring(placeName) .. "' not found in Dictionaries.Mobs")
        return false
    end
    
    for islandName, islandData in pairs(Dictionaries.Mobs[placeName]) do
        if typeof(islandData) == 'table' then
            for questDummy, questData in pairs(islandData) do
                if typeof(questData) == 'table' then
                    for mobName, mobValue in pairs(questData) do
                        if mobName == mob then
                            if questDummy == 'No Quest' then
                                return false
                            else
                                return true
                            end
                        end
                    end
                end
            end
        end
    end
    return false
end

local function hasQuests()
    if #Quests2:GetChildren() == 0 then
        return false
    end
    
    if #Quests2:GetChildren() ~= Stats.Quests.Value then
        return false
    end
    
    local matchingQuests = 0
    
    for i = 1, Stats.Quests.Value do
        local questSlot = Quests2:FindFirstChild(tostring(i))
        if questSlot and questSlot:FindFirstChild('Targets') then
            if questSlot.Targets[Options.selectedMob.Value] then
                matchingQuests = matchingQuests + 1
            end
        end
    end
    
    return matchingQuests == Stats.Quests.Value
end

local function getMobQuestInfo()
    if not Options.selectedMob.Value then
        return nil, nil, nil
    end
    
    if not Dictionaries.Mobs[placeName] then
        return nil, nil, nil
    end
    
    for islandName, islandData in pairs(Dictionaries.Mobs[placeName]) do
        if typeof(islandData) == 'table' then
            for questDummy, questData in pairs(islandData) do
                if typeof(questData) == 'table' then
                    for mobName, questIndex in pairs(questData) do
                        if mobName == Options.selectedMob.Value then
                            if questDummy == 'No Quest' then
                                return nil, nil, nil
                            else
                                return questDummy, questIndex, islandName
                            end
                        end
                    end
                end
            end
        end
    end
    
    return nil, nil, nil
end

local function autoQuest()
    print("AutoQuest called - hasQuests:", hasQuests())
    if not IsAlive() then return false end

    if doesMobHaveAQuest(Options.selectedMob.Value) then
        if not hasQuests() then
            local questDummy, questIndex, questIsland = getMobQuestInfo()
            if not questDummy then
                print("Quest info not available, will retry next iteration...")
                return false -
            end

            setSpawn(questIsland)
            task.wait(2)
            if Options.mobilityMethod.Value == 'Teleport' then
                Teleport(Interactables:FindFirstChild(questDummy):FindFirstChild('HumanoidRootPart'))
                repeat
                    task.wait()
                    Remotes:WaitForChild("quest"):FireServer("Accept", {Index = questIndex, Model = Interactables:WaitForChild(questDummy):WaitForChild(questDummy)})
                until hasQuests()
            elseif Options.mobilityMethod.Value == 'Tween' then
                Tween(Interactables:FindFirstChild(questDummy):FindFirstChild('HumanoidRootPart'))
                repeat
                    task.wait()
                    Remotes:WaitForChild("quest"):FireServer("Accept", {Index = questIndex, Model = Interactables:WaitForChild(questDummy):WaitForChild(questDummy)})
                until hasQuests()
            end
        elseif hasQuests() then
            return true
        else
            Remotes:FindFirstChild('quest'):FireServer('Abandon All')
        end
    else
        return true
    end

    return true
end

local function autoFarm()
    if not IsAlive() then return end

    if Toggles.autoQuest.Value then
        local questSuccess = autoQuest()
        if not questSuccess then
            print("Quest failed...")
            task.wait(1)
            return
        end
    end

    if not init() then return end

    return true
end

task.spawn(function()
    while Toggles.autoFarm.Value do
        autoFarm()
        task.wait(0.5)
    end
end)