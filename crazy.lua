local function autoFarm()
    print("uh autofarm crazy i agree with ya")
    if not IsAlive() then
        print("player is dead")
        return 
    end

    print("player is alive")
    
    while Toggles.autoQuest.Value do
        local questSuccess = autoQuest()
        print("quest success: " .. tostring(questSuccess))
        if not questSuccess then
            return 
        end

        -- you can remove this! it will crash other peoples game :)
        task.wait(1)
    end
    
    if not init() then
        return 
    end
    
    return true
end